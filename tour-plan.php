<?php
include "includes/header.php";
include "includes/config.php";
if (!isset($_SESSION['user_id']) && $_SESSION['user_id'] == '') {
    header('Location: index.php');
    exit();
}

// Check if tour parameter exists
if (!isset($_GET['tour']) || empty($_GET['tour'])) {
    header('Location: index.php');
    exit();
}

$tour_id = intval($_GET['tour']);

// Fetch tour data from usertours table
$query = "SELECT * FROM usertours WHERE id = ? AND uid = ?";
$stmt = $db->prepare($query);
$stmt->bind_param("ii", $tour_id, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    // Tour not found or doesn't belong to user, redirect to dashboard
    header('Location: dashboard.php');
    exit();
}

$tour = $result->fetch_assoc();
$stmt->close();

// Fetch destinations/roadmap for this tour using tourdestinationconnection table
$destinations_query = "SELECT d.*, tdc.rank FROM destinations d
                      JOIN tourdestinationconnection tdc ON d.id = tdc.did
                      WHERE tdc.tid = ? AND tdc.uid = ?
                      ORDER BY tdc.rank ASC";
$destinations_stmt = $db->prepare($destinations_query);
$destinations_stmt->bind_param("ii", $tour_id, $_SESSION['user_id']);
$destinations_stmt->execute();
$destinations_result = $destinations_stmt->get_result();
$destinations = $destinations_result->fetch_all(MYSQLI_ASSOC);
$destinations_stmt->close();

// Set default included and not included items (since tables don't exist yet)
$included_items = [
    ['text' => 'Transportation between destinations'],
    ['text' => 'Professional tour guide'],
    ['text' => 'Accommodation as per itinerary'],
    ['text' => 'Breakfast and dinner'],
    ['text' => 'All entrance fees to attractions']
];

$notincluded_items = [
    ['text' => 'International flights'],
    ['text' => 'Personal expenses and shopping'],
    ['text' => 'Travel insurance'],
    ['text' => 'Lunch (unless specified)'],
    ['text' => 'Tips and gratuities']
];
?>
<body class="bg-gray-50 min-h-screen">
    <!-- Mobile Menu Button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <button id="mobile-menu-btn" onclick="toggleMobileMenu()" class="bg-white p-2 rounded-lg shadow-lg">
            <svg class="w-6 h-6 text-pakistan-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu">
            <div class="p-4 border-b">
                <h2 class="text-xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h2>
            </div>
            <nav class="p-4">
                <ul class="space-y-4">
                    <li><a href="dashboard.php" class="block text-gray-700 hover:text-pakistan-green">← All Tours</a></li>
                    
                    <li><button onclick="logout()" class="block w-full text-left text-red-600 hover:text-red-800">Logout</button></li>
                     <li><button  class="block w-full mt-8 text-left text-sm"><?php echo $_SESSION['fullname']; ?></button></li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg relative z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center ml-12 lg:ml-0">
                        <h1 class="text-xl sm:text-2xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h1>
                    </div>
                </div>
                <div class="hidden lg:flex items-center space-x-4">
                    <a href="dashboard.php" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">← Back to All Tours</a>
                    <span class="text-gray-700 text-sm"><?php echo $_SESSION['fullname']; ?></span>
                    <button onclick="logout()" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Logout</button>
                    
                </div>
                <!-- Mobile back button -->
                <div class="lg:hidden flex items-center">
                    <a href="dashboard.php" class="text-pakistan-green text-sm">← Back</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
        <!-- Tour Header -->
        <div class="bg-gradient-to-r from-pakistan-green to-pakistan-light rounded-lg shadow-lg mb-6 sm:mb-8">
            <div class="px-4 sm:px-6 py-6 sm:py-8 text-white">
                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-start space-y-4 lg:space-y-0">
                    <div class="flex-1">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2"><?php echo htmlspecialchars($tour['title']); ?></h1>
                        <p class="text-base sm:text-lg opacity-90 mb-4"><?php echo htmlspecialchars($tour['highlight_destinations']); ?></p>
                        
                        <!-- Mobile-first info grid -->
                        <div class="grid grid-cols-2 lg:flex lg:items-center gap-3 lg:gap-6">
                            <div class="flex items-center">
                                <span class="text-sm opacity-75 mr-2">📅</span>
                                <span class="text-xs sm:text-sm">March 15-25, 2024</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm opacity-75 mr-2">⏱️</span>
                                <span class="text-xs sm:text-sm"><?php echo $tour['duration']; ?> Days</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm opacity-75 mr-2">👥</span>
                                <span class="text-xs sm:text-sm"><?php echo $tour['persons']; ?> People</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm opacity-75 mr-2">💰</span>
                                <span class="text-xs sm:text-sm">$ <?php echo number_format($tour['cost']); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class=" hidden flex flex-col sm:flex-row lg:flex-col items-start sm:items-center lg:items-end space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-0 lg:space-y-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-green-100 text-green-800">
                            Completed
                        </span>
                        <div class="flex items-center">
                            <span class="text-xs sm:text-sm opacity-75 mr-1">Rating:</span>
                            <span class="text-xs sm:text-sm">⭐⭐⭐⭐⭐ 5.0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tour Roadmap -->
        <div class="bg-white rounded-lg shadow-lg p-4 sm:p-6 mb-6 sm:mb-8">
            <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">Tour Roadmap</h2>

            <!-- Timeline -->
            <div class="relative">
                <!-- Vertical line - hidden on mobile, shown on larger screens -->
                <div class="hidden sm:block absolute left-6 lg:left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>

                <?php
                if (empty($destinations)) {
                    echo '<div class="text-center py-8">
                            <p class="text-gray-500">No roadmap available for this tour.</p>
                          </div>';
                } else {
                    $total_destinations = count($destinations);
                    foreach ($destinations as $index => $destination) {
                        $day_number = $index + 1;
                        $is_last = ($index === $total_destinations - 1);

                        // Different colors for different stages
                        $bg_colors = ['bg-pakistan-green', 'bg-blue-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'];
                        $bg_color = $bg_colors[$index % count($bg_colors)];

                        // Truncate text to 64 characters
                        $truncated_text = strlen($destination['text']) > 300 ?
                            substr($destination['text'], 0, 300) . '...' :
                            $destination['text'];

                        echo '<div class="relative flex flex-col sm:flex-row sm:items-start' . ($is_last ? '' : ' mb-6 sm:mb-8') . '">
                                <div class="flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 ' . $bg_color . ' rounded-full flex items-center justify-center text-white font-bold text-sm sm:text-lg z-10 mb-3 sm:mb-0">
                                    ' . $day_number . '
                                </div>
                                <div class="sm:ml-4 lg:ml-6 flex-1">
                                    <div class="bg-gray-50 rounded-lg p-3 sm:p-4">
                                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-2">' . htmlspecialchars($destination['title']) . '</h3>
                                        <p class="text-gray-600 mb-3 break-normal text-wrap">' . htmlspecialchars($truncated_text) . '</p>
                                        ' . (strlen($destination['text']) > 64 ?
                                            '<button onclick="showDestinationDetails(' . $destination['id'] . ', \'' .
                                            htmlspecialchars(addslashes($destination['title'])) . '\', \'' .
                                            htmlspecialchars(addslashes($destination['text'])) . '\', \'' .
                                            htmlspecialchars(!empty($destination['videoLink']) ? $destination['videoLink'] : '') . '\', \'' .
                                            htmlspecialchars(!empty($destination['img1']) ? $destination['img1'] : '') . '\', \'' .
                                            htmlspecialchars(!empty($destination['img2']) ? $destination['img2'] : '') . '\', \'' .
                                            htmlspecialchars(!empty($destination['img3']) ? $destination['img3'] : '') . '\', \'' .
                                            '\', \'' .
                                            '\')"
                                            class="inline-flex items-center px-3 py-1 bg-pakistan-green hover:bg-pakistan-light text-white text-xs sm:text-sm rounded-md transition duration-300 mb-3">
                                                <span class="mr-1">📖</span> Details
                                            </button>' : '') . '
                                        ';

                        // Collect all boxes to display (accommodation + features)
                        $boxes = array();

                        // // Add accommodation if it's not empty
                        // if (!empty(trim($destination['accomodation']))) {
                        //     $boxes[] = array(
                        //         'icon' => '🏨',
                        //         'title' => 'Accommodation',
                        //         'text' => $destination['accomodation']
                        //     );
                        // }

                        // Add features if both title and text are not empty
                        for ($i = 1; $i <= 3; $i++) {
                            $featureTitle = 'featureTitle' . $i;
                            $featureText = 'featureText' . $i;

                            if (!empty(trim($destination[$featureTitle])) && !empty(trim($destination[$featureText]))) {
                                $boxes[] = array(
                                    'icon' => '',
                                    'title' => $destination[$featureTitle],
                                    'text' => $destination[$featureText]
                                );
                            }
                        }

                        // Display all boxes if any exist
                        if (!empty($boxes)) {
                            echo '<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4">';
                            foreach ($boxes as $box) {
                                echo '<div class="bg-white p-2 sm:p-3 rounded border">
                                        <h4 class="font-medium text-xs sm:text-sm text-gray-900">' . $box['icon'] . ' ' . htmlspecialchars($box['title']) . '</h4>
                                        <p class="text-xs sm:text-sm text-gray-600">' . htmlspecialchars($box['text']) . '</p>
                                      </div>';
                            }
                            echo '</div>';
                        }

                        echo '    </div>
                                </div>
                              </div>';
                    }
                }
                ?>
            </div>
        </div>

        <!-- Tour Summary -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-6 sm:mb-8">
            <!-- Inclusions -->
            <div class="bg-white rounded-lg shadow-lg p-4 sm:p-6">
                <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">✅ What's Included</h3>
                <ul class="space-y-2">
                    <?php
                    if (empty($included_items)) {
                        echo '<li class="flex items-start">
                                <span class="text-gray-400 mr-2 mt-0.5">ℹ️</span>
                                <span class="text-sm sm:text-base text-gray-500">No inclusions specified for this tour.</span>
                              </li>';
                    } else {
                        foreach ($included_items as $item) {
                            echo '<li class="flex items-start">
                                    <span class="text-green-500 mr-2 mt-0.5">✓</span>
                                    <span class="text-sm sm:text-base text-gray-700">' . htmlspecialchars($item['text']) . '</span>
                                  </li>';
                        }
                    }
                    ?>
                </ul>
            </div>

            <!-- Exclusions -->
            <div class="bg-white rounded-lg shadow-lg p-4 sm:p-6">
                <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">❌ What's Not Included</h3>
                <ul class="space-y-2">
                    <?php
                    if (empty($notincluded_items)) {
                        echo '<li class="flex items-start">
                                <span class="text-gray-400 mr-2 mt-0.5">ℹ️</span>
                                <span class="text-sm sm:text-base text-gray-500">No exclusions specified for this tour.</span>
                              </li>';
                    } else {
                        foreach ($notincluded_items as $item) {
                            echo '<li class="flex items-start">
                                    <span class="text-red-500 mr-2 mt-0.5">✗</span>
                                    <span class="text-sm sm:text-base text-gray-700">' . htmlspecialchars($item['text']) . '</span>
                                  </li>';
                        }
                    }
                    ?>
                </ul>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
            <button onclick="downloadItinerary()" class="w-full sm:w-auto bg-pakistan-green hover:bg-pakistan-light text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold transition duration-300 text-sm sm:text-base">
                📄 Download PDF
            </button>
            <button onclick="shareTrip()" class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold transition duration-300 text-sm sm:text-base">
                📤 Share Trip
            </button>
           
        </div>

        <!-- Mobile Quick Actions -->
        <div class="lg:hidden mt-6 grid grid-cols-2 gap-4 hidden">
            <button onclick="downloadItinerary()" class="bg-gray-100 text-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">📄</div>
                <div class="text-sm font-medium">Download PDF</div>
            </button>
            <button onclick="shareTrip()" class="bg-gray-100 text-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">📤</div>
                <div class="text-sm font-medium">Share Trip</div>
            </button>
        </div>
    </div>

    <!-- Destination Details Modal -->
    <div id="destinationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 p-4">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-screen overflow-y-auto">
                <div class="sticky top-0 bg-white px-4 sm:px-6 py-4 border-b flex justify-between items-center">
                    <h2 id="modalTitle" class="text-xl sm:text-2xl font-bold text-gray-900"></h2>
                    <button onclick="closeDestinationModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                <div class="p-4 sm:p-6">
                    <!-- Video Section -->
                    <div id="videoSection" class="mb-6" style="display: none;">
                        <div class="aspect-w-16 aspect-h-9 mb-4">
                            <iframe id="destinationVideo"
                                    class="w-full h-64 sm:h-80 lg:h-96 rounded-lg"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    </div>

                    <!-- Image Section -->
                    <div id="imageSection" class="mb-6" style="display: none;">
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="imageGallery">
                            <img id="image1" class="w-full h-32 object-cover rounded-lg " style="display: none;" src="" alt="">
                            <img id="image2" class="w-full h-32 object-cover rounded-lg " style="display: none;" src="" alt="">
                            <img id="image3" class="w-full h-32 object-cover rounded-lg " style="display: none;" src="" alt="">
                        </div>
                    </div>

                    <!-- Text Section -->
                    <div class="bg-gray-50 rounded-lg p-4 sm:p-6 ">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">About This Destination</h3>
                        
                        <p id="modalText" class="text-gray-700 leading-relaxed text-base break-normal text-wrap "></p>
                    </div>
                </div>
                <div class="sticky bottom-0 bg-white px-4 sm:px-6 py-4 border-t flex justify-end">
                    <button onclick="closeDestinationModal()"
                            class="px-6 py-2 bg-pakistan-green hover:bg-pakistan-light text-white rounded-md transition duration-300">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal for Full Size View -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
                <span class="text-3xl">&times;</span>
            </button>
            <img id="fullImage" class="max-w-full max-h-full object-contain rounded-lg" alt="Full size image">
        </div>
    </div>

   <script>
        // Pass tour data to JavaScript
        const tourData = <?php echo json_encode($tour); ?>;
   </script>
   <script src="includes/media/tour-plan.js"></script>
   <script src="includes/media/central.js"></script>
<?php
include "includes/footer.php";
?> 