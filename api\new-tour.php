<?php
include '../includes/config.php';
// API endpoint to process new tour form data and get AI recommendation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and capture all form inputs In Shaa Allah
    $season = isset($_POST['season']) ? trim($_POST['season']) : '';
    $duration = isset($_POST['duration']) ? intval($_POST['duration']) : 0;
    $budget = isset($_POST['budget']) ? trim($_POST['budget']) : '';
    $special_requirements = isset($_POST['special_requirements']) ? trim($_POST['special_requirements']) : '';
    $willing_to_drive = isset($_POST['willing_to_drive']) ? trim($_POST['willing_to_drive']) : '';
    $tour_type = isset($_POST['tour_type']) ? trim($_POST['tour_type']) : '';
    $focused_region = isset($_POST['focused_region']) ? trim($_POST['focused_region']) : '';
    $tour_action = isset($_POST['tour_action']) ? trim($_POST['tour_action']) : '';
    $starting_city = isset($_POST['starting_city']) ? trim($_POST['starting_city']) : '';
    // Process travelers data (dynamic array)
    $travelers = [];
    if (isset($_POST['traveler_age']) && isset($_POST['traveler_gender'])) {
        $ages = $_POST['traveler_age'];
        $genders = $_POST['traveler_gender'];

        for ($i = 0; $i < count($ages); $i++) {
            if (!empty($ages[$i]) && !empty($genders[$i])) {
                $travelers[] = [
                    'age' => intval($ages[$i]),
                    'gender' => trim($genders[$i])
                ];
            }
        }
    }
    // Build comprehensive user requirements string In Shaa Allah
    $userRequirements = "User Tour Requirements:\n";

    // Season preference
    if (!empty($season)) {
        $seasonNames = [
            'spring' => 'Spring (March-May)',
            'summer' => 'Summer (May-September)',
            'autumn' => 'Autumn (September-November)',
        ];
        $userRequirements .= "- Preferred Season: " . ($seasonNames[$season] ?? $season) . "\n";
    }

    // Duration
    if ($duration > 0) {
        $userRequirements .= "- Duration: {$duration} days\n";
    }

    // Budget
    if (!empty($budget)) {
        $userRequirements .= "- Budget Range: {$budget}\n";
    }
    if (!empty($tour_type)) {
        $userRequirements .= "- Tour Type: {$tour_type}\n";
    }
    if (!empty($focused_region)) {
        $userRequirements .= "- Focused Region: {$focused_region}\n";
    }
    if (!empty($tour_action)) {
        $userRequirements .= "- Tour Action: {$tour_action}\n";
    }
    if (!empty($willing_to_drive)) {
        $userRequirements .= "- Willing to Drive: {$willing_to_drive}\n";
    }
    // Travelers information
    if (!empty($travelers)) {
        $userRequirements .= "- Number of Travelers: " . count($travelers) . "\n";
        $userRequirements .= "- Traveler Details:\n";
        foreach ($travelers as $index => $traveler) {
            $userRequirements .= "  * Traveler " . ($index + 1) . ": Age {$traveler['age']}, Gender: {$traveler['gender']}\n";
        }
    }

    // Special requirements
    if (!empty($special_requirements)) {
        $userRequirements .= "- Special Requirements: {$special_requirements}\n";
    }
    if (!empty($starting_city)) {
        $userRequirements .= "- Starting City: {$starting_city}\n";
    }
    // Add fallback if no requirements provided
    if (trim($userRequirements) === "User Tour Requirements:") {
        $userRequirements .= "- No specific requirements provided. Please suggest a popular tour.\n";
    }

    // Fetch all destinations from database In Shaa Allah
    $sql = "SELECT id, title, text, featureTitle1, featureText1, featureTitle2, featureText2, featureTitle3, featureText3 FROM destinations";
    $result = $db->query($sql);

    // Build destinations information string
    $destinationsInfo = "Available Destinations in Pakistan:\n";
    $destinationsArray = [];

    if ($result && $result->num_rows > 0) {
        while($destination = $result->fetch_assoc()) {
            $destinationsArray[] = $destination;
            $destinationsInfo .= "- ID: {$destination['id']}, Title: {$destination['title']}\n";
            if (!empty($destination['text'])) {
                $destinationsInfo .= "  Description: " . substr($destination['text'], 0, 200) . "...\n";
            }
            if (!empty($destination['featureTitle1'])) {
                $destinationsInfo .= "  Feature 1: {$destination['featureTitle1']} - {$destination['featureText1']}\n";
            }
            if (!empty($destination['featureTitle2'])) {
                $destinationsInfo .= "  Feature 2: {$destination['featureTitle2']} - {$destination['featureText2']}\n";
            }
            if (!empty($destination['featureTitle3'])) {
                $destinationsInfo .= "  Feature 3: {$destination['featureTitle3']} - {$destination['featureText3']}\n";
            }
            $destinationsInfo .= "\n";
        }
    } else {
        $destinationsInfo .= "No destinations found in database.\n";
    }

    // Get user ID from session
    $userId = $_SESSION['user_id'];

    // Prepare data for the API request
    $data = array(
        'model' => 'mistral-ai/Mistral-Large-2411',
        'messages' => array(
            array(
                'role' => 'system',
                'content' => "You are a helpful travel assistant that creates custom tours in Pakistan. Here are all available destinations:\n\n$destinationsInfo\n\nAnalyze the user's detailed requirements and create a custom tour plan. Consider factors like:\n- Season preferences and weather conditions\n- Duration matching\n- Budget compatibility\n- Group size and traveler demographics\n- Special requirements\n- Starting city preferences\n- Regional focus\n\nIn Shaa Allah, return ONLY a valid JSON object with this exact structure:\n{\n  \"title\": \"Tour Title\",\n  \"persons\": number_of_persons,\n  \"cost\": estimated_cost_per_person,\n  \"highlight_destinations\": \"Destination1 • Destination2 • Destination3\",\n  \"duration\": number_of_days,\n  \"icon\": \"appropriate_emoji\",\n  \"text\": \"Tour description\",\n  \"season\": season_number_0_summer_1_winter_2_monsoon_3_mid_season,\n  \"iconColor\": \"gradient_class_like_from-pakistan-green_to-pakistan-light\",\n  \"destinations\": [\n    {\"id\": destination_id, \"rank\": 1},\n    {\"id\": destination_id, \"rank\": 2}\n  ]\n}\n\nSelect 2-5 destinations from the available list that best match the requirements. Rank them in order of visit (1 = first destination, 2 = second, etc.)."
            ),
            array(
                'role' => 'user',
                'content' => $userRequirements
            )
        ),
        'temperature' => 0.3,
        'top_p' => 1
    );

    // Initialize cURL session
    $ch = curl_init('https://models.github.ai/inference/chat/completions');
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Authorization: Bearer ****************************************' 
    ));

    // Execute cURL request
    $response = curl_exec($ch);
    
    // Check for errors
    if(curl_errno($ch)) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Curl error: ' . curl_error($ch)
        ));
        exit;
    }
    
    curl_close($ch);

    // Parse response and extract AI-generated tour data
    $responseData = json_decode($response, true);
    $aiResponse = isset($responseData['choices'][0]['message']['content'])
        ? $responseData['choices'][0]['message']['content']
        : 'No response generated';

    // Clean the AI response by removing markdown code blocks if present
    $cleanedResponse = $aiResponse;

    // Remove ```json and ``` markers if they exist
    $cleanedResponse = preg_replace('/^```json\s*/', '', $cleanedResponse);
    $cleanedResponse = preg_replace('/\s*```$/', '', $cleanedResponse);
    $cleanedResponse = trim($cleanedResponse);

    // Try to parse the cleaned AI response as JSON
    $tourData = json_decode($cleanedResponse, true);

    if ($tourData && is_array($tourData)) {
        // Add user ID to the tour data
        $tourData['uid'] = $userId;

        // Return the structured tour data for add-tour.php to process
        echo json_encode(array(
            'success' => true,
            'tourData' => $tourData,
            'message' => 'Tour plan generated successfully In Shaa Allah'
        ));
    } else {
        // If JSON parsing fails, return the responses for debugging
        echo json_encode(array(
            'success' => false,
            'message' => 'Failed to parse AI response as valid JSON',
            'raw_response' => $aiResponse,
            'cleaned_response' => $cleanedResponse,
            'json_error' => json_last_error_msg()
        ));
    }

}
?>