-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 16, 2025 at 08:10 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `dabdab`
--

-- --------------------------------------------------------

--
-- Table structure for table `destinations`
--

CREATE TABLE `destinations` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `text` text NOT NULL,
  `videoLink` varchar(500) NOT NULL,
  `featureTitle1` varchar(255) NOT NULL,
  `featureText1` varchar(255) NOT NULL,
  `featureTitle2` varchar(255) NOT NULL,
  `featureText2` varchar(255) NOT NULL,
  `featureTitle3` varchar(255) NOT NULL,
  `featureText3` varchar(255) NOT NULL,
  `img1` varchar(255) NOT NULL,
  `img2` varchar(255) NOT NULL,
  `img3` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `destinations`
--

INSERT INTO `destinations` (`id`, `title`, `text`, `videoLink`, `featureTitle1`, `featureText1`, `featureTitle2`, `featureText2`, `featureTitle3`, `featureText3`, `img1`, `img2`, `img3`) VALUES
(1, 'Departure from Islamabad', 'good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good ', 'https://www.youtube.com/embed/64RVtjiDTuY?si=a7OZ4ilcN41ZVZmQ', ' ✨ Accomodation', 'Nexted', '', '', '', '', 'https://hunzaexplorers.com/storage/2022/06/Islamabad.jpg', 'dsf', ''),
(2, 'Journey to Hunza Valley', '', '', '', '', '', '', '', '', '', '', ''),
(3, 'Highest Cold Dessert', '', '', '', '', '', '', '', '', '', '', ''),
(4, 'Lahore', 'A City Of Gardans', '', '', '', '', '', '', '', '', '', '');

-- --------------------------------------------------------

--
-- Table structure for table `tourdestinationconnection`
--

CREATE TABLE `tourdestinationconnection` (
  `id` int(11) NOT NULL,
  `uid` int(11) NOT NULL,
  `tid` int(11) NOT NULL,
  `did` int(11) NOT NULL,
  `rank` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tourdestinationconnection`
--

INSERT INTO `tourdestinationconnection` (`id`, `uid`, `tid`, `did`, `rank`) VALUES
(1, 4, 5, 1, 1),
(2, 4, 5, 2, 2),
(3, 4, 5, 3, 3),
(4, 4, 6, 1, 1),
(5, 4, 6, 4, 2),
(6, 4, 7, 2, 1),
(7, 4, 7, 3, 2),
(8, 4, 7, 4, 3);

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `email`, `fullname`, `password`) VALUES
(4, '<EMAIL>', 'Husnain Gilani', '$2y$10$3nR2j/y9i/aYdIWSCzSukeYC2shLGCMyqATwGTJkBL2/zfixIYjx2');

-- --------------------------------------------------------

--
-- Table structure for table `usertours`
--

CREATE TABLE `usertours` (
  `id` int(11) NOT NULL,
  `date` date NOT NULL DEFAULT current_timestamp(),
  `uid` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `persons` int(11) NOT NULL,
  `cost` int(11) NOT NULL,
  `highlight_destinations` varchar(255) NOT NULL,
  `duration` int(11) NOT NULL,
  `icon` text NOT NULL,
  `text` text NOT NULL,
  `season` int(11) NOT NULL COMMENT '0=summer, 1=winter, 2=monsoon, 3=mid season',
  `iconColor` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `usertours`
--

INSERT INTO `usertours` (`id`, `date`, `uid`, `title`, `persons`, `cost`, `highlight_destinations`, `duration`, `icon`, `text`, `season`, `iconColor`) VALUES
(5, '2025-06-04', 4, 'Northern Pakistan Adventure', 4, 1200, 'Islamabad • Hunza Valley • Skardu', 7, '🏔️', 'Experience the breathtaking beauty of Northern Pakistan', 1, 'from-pakistan-green to-pakistan-light'),
(6, '2025-06-04', 4, 'Cultural Heritage Tour', 2, 800, 'Islamabad • Lahore', 5, '🏛️', 'Discover Pakistan\'s rich cultural heritage', 0, 'from-blue-500 to-purple-500'),
(7, '2025-06-05', 4, 'Mountain Explorer', 3, 1500, 'Hunza Valley • Skardu • Lahore', 10, '⛰️', 'Ultimate mountain adventure experience', 2, 'from-orange-500 to-red-500');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `destinations`
--
ALTER TABLE `destinations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tourdestinationconnection`
--
ALTER TABLE `tourdestinationconnection`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `usertours`
--
ALTER TABLE `usertours`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `destinations`
--
ALTER TABLE `destinations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `tourdestinationconnection`
--
ALTER TABLE `tourdestinationconnection`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `usertours`
--
ALTER TABLE `usertours`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
