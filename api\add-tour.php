<?php
include '../includes/config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data
    $data = json_decode(file_get_contents('php://input'), true);

    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User not logged in']);
        exit;
    }

    $userId = $_SESSION['user_id'];

    // Validate required tour data
    if (!isset($data['tourData']) || !is_array($data['tourData'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid tour data provided']);
        exit;
    }

    $tourData = $data['tourData'];

    // Validate required fields
    $requiredFields = ['title', 'persons', 'cost', 'highlight_destinations', 'duration', 'icon', 'text', 'season', 'iconColor'];
    foreach ($requiredFields as $field) {
        if (!isset($tourData[$field])) {
            echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
            exit;
        }
    }

    // Start transaction
    $db->autocommit(false);

    try {
        // Insert new tour into usertours table
        $sql = "INSERT INTO usertours (uid, title, persons, cost, highlight_destinations, duration, icon, text, season, iconColor)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $db->error);
        }

        $stmt->bind_param("isisisssis",
            $userId,
            $tourData['title'],
            $tourData['persons'],
            $tourData['cost'],
            $tourData['highlight_destinations'],
            $tourData['duration'],
            $tourData['icon'],
            $tourData['text'],
            $tourData['season'],
            $tourData['iconColor']
        );

        if (!$stmt->execute()) {
            throw new Exception("Failed to insert tour: " . $stmt->error);
        }

        $newTourId = $db->insert_id;
        $stmt->close();

        // Insert destinations into tourdestinationconnection table
        if (isset($tourData['destinations']) && is_array($tourData['destinations'])) {
            $destSql = "INSERT INTO tourdestinationconnection (uid, tid, did, rank) VALUES (?, ?, ?, ?)";
            $destStmt = $db->prepare($destSql);

            if (!$destStmt) {
                throw new Exception("Prepare failed for destinations: " . $db->error);
            }

            foreach ($tourData['destinations'] as $destination) {
                if (isset($destination['id']) && isset($destination['rank'])) {
                    $destStmt->bind_param("iiii", $userId, $newTourId, $destination['id'], $destination['rank']);
                    if (!$destStmt->execute()) {
                        throw new Exception("Failed to insert destination connection: " . $destStmt->error);
                    }
                }
            }
            $destStmt->close();
        }

        // Commit transaction
        $db->commit();
        $db->autocommit(true);

        echo json_encode([
            'success' => true,
            'message' => 'Tour created successfully In Shaa Allah',
            'tourId' => $newTourId
        ]);

    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollback();
        $db->autocommit(true);

        echo json_encode([
            'success' => false,
            'message' => 'Failed to create tour: ' . $e->getMessage()
        ]);
    }
}
?>