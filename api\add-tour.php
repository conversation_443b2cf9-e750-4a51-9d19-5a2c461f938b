<?php
include '../includes/config.php';
// get and sinative tourId
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data
    $data = json_decode(file_get_contents('php://input'), true);
    $tourId = isset($data['tourId']) ? intval($data['tourId']) : 0;

    if ($tourId > 0) {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            echo json_encode(['success' => false, 'message' => 'User not logged in']);
            exit;
        }

        // Insert new tour for user (create empty usertours record)
        $userId = $_SESSION['user_id'];
        $sql = "INSERT INTO usertours (uid, title, persons, cost, highlight_destinations, duration, icon, text, season, iconColor)
                VALUES (?, 'Custom Tour Plan', 0, 0, 'AI-generated destinations based on your preferences', 0, '🏔️', '', 0, 'from-pakistan-green to-pakistan-light')";
        $stmt = $db->prepare($sql);
        $stmt->bind_param("i", $userId);

        if ($stmt->execute()) {
            $newTourId = $db->insert_id;
            echo json_encode(['success' => true, 'response' => $newTourId]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add tour']);
        }
        $stmt->close();
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid tour ID']);
    }
}
    