 function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menu = document.getElementById('mobile-menu');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    menu.classList.remove('-translate-x-full');
                }, 10);
            } else {
                menu.classList.add('-translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }

       

        function downloadItinerary() {
            // Check if tour data is available
            if (typeof tourData === 'undefined') {
                alert('Tour data not available. Please refresh the page and try again.');
                return;
            }

            // Check if pdfLink exists and is not empty
            if (!tourData.pdfLink || tourData.pdfLink.trim() === '') {
                alert('PDF itinerary is not available for this tour.');
                return;
            }

            // Create a temporary link element and trigger download
            const link = document.createElement('a');
            link.href = tourData.pdfLink;
            link.download = `${tourData.title || 'Tour'}_Itinerary.pdf`;
            link.target = '_blank'; // Open in new tab as fallback

            // Append to body, click, and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function shareTrip() {
            if (navigator.share) {
                navigator.share({
                    title: 'Visit Pakistan Trip',
                    text: 'Check out my amazing trip to Pakistan!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Trip link copied to clipboard!');
                });
            }
        }

        function showDestinationDetails(id, title, text, videoLink, img1, img2, img3) {
            // Set modal content
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalText').textContent = text;

            // Handle video
            const videoSection = document.getElementById('videoSection');
            const videoIframe = document.getElementById('destinationVideo');

            

            // Handle image section
            const imageSection = document.getElementById('imageSection');
            const imageGallery = document.getElementById('imageGallery');
            const image1 = document.getElementById('image1');
            const image2 = document.getElementById('image2');
            const image3 = document.getElementById('image3');
            // Priority: Video first, then images
            if (videoLink && videoLink.trim() !== '' && videoLink !== 'null') {
                // Show video, hide images
                videoIframe.src = videoLink;
                videoSection.style.display = 'block';
                if (img1 || img2 || img3) {
                if (imageSection) {
                    imageSection.style.display = 'block';
                    // Only show images that are not null or empty
                    if (img1 && img1.trim() !== '' && img1 !== 'null') {
                        image1.src = img1;
                        image1.style.display = 'block';
                    } else {
                        image1.style.display = 'none';
                    }
                    if (img2 && img2.trim() !== '' && img2 !== 'null') {
                        image2.src = img2;
                        image2.style.display = 'block';
                    } else {
                        image2.style.display = 'none';
                    }
                    if (img3 && img3.trim() !== '' && img3 !== 'null') {
                        image3.src = img3;
                        image3.style.display = 'block';
                    } else {
                        image3.style.display = 'none';
                    }
                }
            }
            } else if (img1 || img2 || img3) {
                // Show images, hide video
                videoIframe.src = '';
                videoSection.style.display = 'none';
                if (imageSection) {
                    imageSection.style.display = 'block';
                    // Only show images that are not null or empty
                    if (img1 && img1.trim() !== '' && img1 !== 'null') {
                        image1.src = img1;
                        image1.style.display = 'block';
                    } else {
                        image1.style.display = 'none';
                    }
                    if (img2 && img2.trim() !== '' && img2 !== 'null') {
                        image2.src = img2;
                        image2.style.display = 'block';
                    } else {
                        image2.style.display = 'none';
                    }
                    if (img3 && img3.trim() !== '' && img3 !== 'null') {
                        image3.src = img3;
                        image3.style.display = 'block';
                    } else {
                        image3.style.display = 'none';
                    }
                }
            } else {
                // No media available
                videoIframe.src = '';
                videoSection.style.display = 'none';
                
            }

            // Show modal
            document.getElementById('destinationModal').classList.remove('hidden');
        }

      
        // Add event listeners for better UX
        document.addEventListener('DOMContentLoaded', function() {
            // Close image modal when clicking outside the image
            document.getElementById('imageModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImageModal();
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!document.getElementById('imageModal').classList.contains('hidden')) {
                        closeImageModal();
                    } else if (!document.getElementById('destinationModal').classList.contains('hidden')) {
                        closeDestinationModal();
                    }
                }
            });
        });

        function setupImageGallery(images) {
            const imageGallery = document.getElementById('imageGallery');
            if (!imageGallery) return;

            // Clear existing images
            imageGallery.innerHTML = '';

            // Create image thumbnails
            images.forEach((imageSrc, index) => {
                const imageContainer = document.createElement('div');
                imageContainer.className = 'cursor-pointer hover:opacity-75 transition-opacity duration-300';

                const img = document.createElement('img');
                img.src = imageSrc;
                img.alt = `Destination image ${index + 1}`;
                img.className = 'w-full h-32 object-cover rounded-lg';
                img.onclick = () => openImageModal(imageSrc);

                imageContainer.appendChild(img);
                imageGallery.appendChild(imageContainer);
            });
        }

        function openImageModal(imageSrc) {
            const imageModal = document.getElementById('imageModal');
            const fullImage = document.getElementById('fullImage');

            if (imageModal && fullImage) {
                fullImage.src = imageSrc;
                imageModal.classList.remove('hidden');
            }
        }

        function closeImageModal() {
            const imageModal = document.getElementById('imageModal');
            if (imageModal) {
                imageModal.classList.add('hidden');
            }
        }

        function closeDestinationModal() {
            // Hide modal
            document.getElementById('destinationModal').classList.add('hidden');

            // Stop video playback by clearing src
            document.getElementById('destinationVideo').src = '';
        }



        // Close mobile menu when clicking overlay
        document.getElementById('mobile-menu-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });

        // Close destination modal when clicking overlay
        document.getElementById('destinationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDestinationModal();
            }
        });

